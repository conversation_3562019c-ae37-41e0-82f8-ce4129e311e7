# 🚀 CrewAI: Build Autonomous AI Agent Teams

This repository serves as a comprehensive guide and hands-on resource for learning and mastering CrewAI, a powerful Python framework designed for building collaborative AI agent systems. Dive in to understand how specialized AI agents can work together seamlessly to tackle complex problems, automate workflows, and deliver intelligent solutions.

## ✨ What is CrewAI?

**CrewAI is a lean, lightning-fast Python framework built entirely from scratch**, independent of other agent frameworks, that empowers developers to create **autonomous AI agents tailored to any scenario**. It allows you to orchestrate teams of AI agents, each with specific roles, goals, and tools, to collaboratively achieve a common objective, much like a well-oiled human team.

### Key Benefits:
*   **Autonomous Problem Solving**: Agents can perform tasks, make decisions, use tools, and communicate effectively.
*   **Collaborative Intelligence**: Enables multiple agents with distinct skills to work together, leading to outcomes greater than a single AI could achieve.
*   **Flexible Workflows**: Supports various process flows, from sequential execution to complex hierarchical structures.
*   **Production-Ready**: Designed for enterprise-grade AI automation with features like observability and customizable LLM integrations.

## 💡 Core Concepts

Understanding these fundamental components is crucial for building effective CrewAI applications:

*   **Agents**: These are the **autonomous units** within CrewAI. Each agent is a specialized "team member" with defined attributes that shape its behavior.
    *   **Role**: Defines the agent's function and area of expertise (e.g., "Research Analyst", "Senior Python Developer").
    *   **Goal**: The individual objective guiding the agent's decision-making.
    *   **Backstory**: Provides context and personality, enriching interactions and influencing how the agent approaches tasks.
    *   **LLM (Large Language Model)**: The underlying intelligence that powers the agent's understanding and response generation. You can specify a particular LLM for an agent, or it defaults to the crew's LLM.
    *   **Tools**: Capabilities or functions available to the agent (e.g., web scraping, search, custom functions).
    *   **Memory**: Agents can maintain context and remember past interactions across multiple tasks.
    *   **Reasoning**: Agents can reflect and create a plan before executing tasks, enabling more strategic problem-solving.
    *   **Delegation**: Agents can be configured to delegate tasks to other agents (`allow_delegation`).

*   **Tasks**: A **specific assignment** to be completed by an agent.
    *   **Description**: A clear and detailed statement of the task's purpose and execution instructions.
    *   **Expected Output**: Defines what the final result should look like, including format and quality criteria.
    *   **Agent Assignment**: Tasks are assigned to a specific agent, or the Crew's process can delegate it.
    *   **Context**: Tasks can depend on the output of previous tasks, enabling a flow of information between agents.
    *   **Human Input**: Tasks can be configured to require human review before the agent delivers its final answer.
    *   **Structured Output**: Tasks can be configured to return output in a specific structured format, such as a Pydantic model or JSON.

*   **Crews**: A **collaborative group of agents** working together to achieve a set of tasks.
    *   **Agents & Tasks**: A crew consists of a list of agents and tasks it needs to accomplish.
    *   **Process**: Defines the **workflow management system** for task execution and agent collaboration. The primary processes are `sequential` (default) and `hierarchical`.
        *   **Sequential Process**: Tasks are executed one after another in a predefined order.
        *   **Hierarchical Process**: A manager agent coordinates the workflow, delegates tasks, and validates outcomes. You can define a custom manager agent or specify a manager LLM.
    *   **Kickoff**: The method to start the crew's work. Can be synchronous (`kickoff()`), asynchronous (`kickoff_async()`), or for multiple inputs (`kickoff_for_each()`).

*   **Flows**: Offer **event-driven orchestration** for structured workflows, allowing granular control over execution paths and state transitions. Flows can natively integrate Crews for enhanced autonomy.

*   **Tools**: External capabilities that agents can utilize to perform various actions beyond their inherent LLM abilities, such as web scraping, searching, or interacting with external APIs. CrewAI supports its own toolkit, LangChain Tools, and allows for custom tool creation.

*   **Knowledge**: A system that allows AI agents to access and utilize **external information sources** during their tasks, effectively acting as a reference library. This enhances agents with domain-specific information and grounds responses in factual data. Knowledge sources can include raw strings, text files, PDF documents, CSV files, Excel spreadsheets, JSON documents, and web content. Knowledge can be applied at either the agent level or the crew level.

## 🚀 Getting Started

Follow these steps to set up your CrewAI project and run your first AI crew:

### 1. Installation

**Prerequisites**:
*   **Python**: CrewAI requires Python **`>=3.10 and <3.14`**.
*   **`uv`**: CrewAI uses `uv` for dependency management. Install it if you don't have it:
    ```bash
    pip install uv
    ```
    If you encounter PATH warnings, run `uv tool update-shell`.

**Install CrewAI CLI**:
```bash
uv tool install crewai
```
Verify the installation with `uv tool list`. You should see `crewai vX.Y.Z` listed.

### 2. Create Your First CrewAI Project

CrewAI provides a CLI tool to scaffold a new project with a recommended YAML structure:
```bash
crewai create crew my-first-crew
cd my-first-crew
```
This command generates a project directory with essential files: `agents.yaml`, `tasks.yaml`, `.env`, `main.py`, `crew.py`, and `tools/`.

### 3. Configure API Keys

CrewAI utilizes LLMs and tools that often require API keys. Store them in your project's `.env` file (which is automatically generated and ignored by Git for security):
```env
# Example for OpenAI and SerperDevTool
OPENAI_API_KEY=your_openai_api_key_here
SERPER_API_KEY=your_serper_api_key_here
```
Refer to the [LLMs documentation](#llms-large-language-models) for specific provider configurations.

### 4. Customize Agents and Tasks

The `agents.yaml` and `tasks.yaml` files define your crew's behavior. Open these files (`src/my_first_crew/config/agents.yaml` and `src/my_first_crew/config/tasks.yaml`) and modify them to fit your needs. Variables like `{topic}` in YAML files will be replaced by inputs from your `main.py` when the crew runs.

**Example `agents.yaml` (from source)**:
```yaml
researcher:
  role: >
    {topic} Senior Data Researcher
  goal: >
    Uncover cutting-edge developments in {topic}
  backstory: >
    You're a seasoned researcher with a knack for uncovering the latest
    developments in {topic}. Known for your ability to find the most relevant
    information and present it in a clear and concise manner.

reporting_analyst:
  role: >
    {topic} Reporting Analyst
  goal: >
    Create detailed reports based on {topic} data analysis and research findings
  backstory: >
    You're a meticulous analyst with a keen eye for detail. You're known for
    your ability to turn complex data into clear and concise reports, making
    it easy for others to understand and act on the information you provide.
```

**Example `tasks.yaml` (from source)**:
```yaml
research_task:
  description: >
    Conduct a thorough research about {topic}
    Make sure you find any interesting and relevant information given
    the current year is 2025.
  expected_output: >
    A list with 10 bullet points of the most relevant information about {topic}
  agent: researcher

reporting_task:
  description: >
    Review the context you got and expand each topic into a full section for a report.
    Make sure the report is detailed and contains any and all relevant information.
  expected_output: >
    A fully fledge reports with the mains topics, each with a full section of information.
    Formatted as markdown without '```'
  agent: reporting_analyst
  output_file: report.md
```
**Important**: Ensure the names in your YAML files (e.g., `researcher`, `research_task`) match the method names in your `crew.py` file for proper linking.

### 5. Run Your Crew

Navigate to your project root in the terminal and run:
```bash
crewai install
crewai run
```
The `crewai install` command locks and installs dependencies based on your project configuration. The `crewai run` command executes your crew, and you'll see the agents' thought processes, actions, and outputs in real-time. A final report (e.g., `report.md`) will be generated in your project's root.

## ⚙️ Advanced Features & Guides

CrewAI offers extensive customization and advanced capabilities for building sophisticated AI applications:

*   **Coding Agents**: Enable agents to write and execute code by setting `allow_code_execution=True`. This is useful for computational tasks and relies on Docker for safe execution by default.
*   **Custom Tools**: Create your own specialized tools for agents to use. You can define them by subclassing `BaseTool` or using the `@tool` decorator.
*   **Custom LLM Implementations**: Integrate any LLM provider not natively supported by LiteLLM by extending the `BaseLLM` abstract class.
*   **Multimodal Agents**: Build agents that can process and analyze both text and non-text content, such as images. Enable by setting `multimodal=True`.
*   **Human Input on Execution**: Introduce human-in-the-loop interactions where an agent prompts for user input before finalizing its answer. Set `human_input=True` in a task definition.
*   **Force Tool Output as Result**: Ensure that the raw output of a tool is directly captured as the task result without agent modification using `result_as_answer=True` when adding a tool.
*   **Conditional Tasks**: Implement dynamic workflows where task execution depends on the outcome of previous tasks. This enhances flexibility and efficiency.
*   **Structured Output**: Define the expected output of agents and tasks using Pydantic models or JSON structures to ensure consistent, machine-readable results.
*   **Asynchronous Execution**: Run tasks or entire crews asynchronously using `async_execution=True` for tasks or `kickoff_async()` for crews, useful for long-running operations.
*   **Kickoff Crew for Each**: Efficiently process multiple items by kicking off the same crew for each item in a list using `kickoff_for_each()`.
*   **Prompt Customization**: Gain low-level control over agent prompts using custom templates (`system_template`, `prompt_template`, `response_template`) or by providing a custom JSON prompt file to the crew. This allows optimization for specific LLM models.
*   **Knowledge Sources**: Beyond basic context, provide agents with a specialized "reference library" of documents (PDFs, CSVs, text files, web content, etc.) to draw information from. Knowledge can be initialized at the agent or crew level, influencing what information is accessible.
*   **Replay Tasks**: Debug and iterate by replaying a crew's execution from a specific task ID, retaining context from previous steps.

## 📊 Observability & Debugging

Monitoring and debugging your CrewAI agents is crucial for optimizing performance and troubleshooting issues:

*   **Verbose Logging**: Set `verbose=True` for agents and/or crews to enable detailed execution logs, showing the agents' thought processes, actions, and outputs in real-time.
*   **Log Files**: Capture execution logs to a file by setting `output_log_file=True` in your Crew configuration. Logs can be saved in `.txt` or `.json` format.
*   **Observability Integrations**: CrewAI integrates with various observability platforms to provide comprehensive tracing, cost tracking, and performance metrics:
    *   **Arize Phoenix**: Integrates with OpenTelemetry and OpenInference for tracing and evaluation of AI applications.
    *   **LangDB AI Gateway**: Provides OpenAI-compatible APIs, access to 350+ LLMs, and end-to-end tracing of CrewAI workflows.
    *   **Langfuse**: An open-source LLM engineering platform offering tracing and monitoring capabilities for debugging, analysis, and optimization.
    *   **Langtrace**: Monitors LLM token and cost usage, visualizes execution flow, supports dataset curation, and prompt versioning.
    *   **MLflow**: Provides a tracing dashboard, automated tracing, OpenTelemetry compatibility, and evaluation features for CrewAI agents.
*   **Knowledge Storage Transparency**: Understand where CrewAI stores knowledge files (using ChromaDB by default) for debugging and management. You can control storage locations via environment variables or custom configurations.
*   **Memory Management**: Agents maintain memory of interactions. For issues with large data, consider using RAG tools or knowledge sources instead of relying solely on the context window.
*   **Reset Memories**: Use `crew.reset_memories(command_type='knowledge')` or the CLI command `crewai reset-memories --knowledge` to clear stored knowledge or agent-specific memories, especially when updating knowledge sources or encountering embedding dimension mismatches.

## 🏢 Enterprise Options

For teams and organizations, CrewAI offers deployment solutions that streamline setup and management:

*   **CrewAI Enterprise (SaaS)**: A zero-installation, cloud-hosted platform for building and deploying crews with a visual agent builder and studio. Offers automatic updates, managed infrastructure, and no-code crew creation.
*   **CrewAI Factory (Self-hosted)**: Provides containerized deployment options for your own infrastructure, offering integration with existing security systems and supporting various hyperscalers.

## 🌐 Community & Resources

Join the vibrant CrewAI community to connect with other developers, share your projects, and get support:

*   **Official Website**: [https://www.crewai.com/](https://www.crewai.com/)
*   **Documentation**: [https://docs.crewai.com/](https://docs.crewai.com/)
*   **GitHub Repository**: Explore examples and contribute to the open-source project.
*   **Community Forum**: [https://community.crewai.com/](https://community.crewai.com/)
*   **Discord**: Join the official Discord server for real-time discussions.
*   **DeepLearning.AI Course**: A dedicated course to learn about Multi AI Agent Systems with CrewAI.

This `README.md` provides a comprehensive overview and practical guidance for getting started and becoming proficient with CrewAI. Experiment with different configurations, explore the advanced features, and join the community to further your CrewAI journey!